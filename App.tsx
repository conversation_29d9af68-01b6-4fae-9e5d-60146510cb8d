import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, Platform, ScrollView, TouchableOpacity, Modal, Linking } from 'react-native';
import { MobileAds } from 'react-native-google-mobile-ads';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import PhoneNumberInput from './src/components/PhoneInput/index';
import MessageInput from './src/components/MessageInput/index';
import RecentLinks from './src/components/RecentLinks/index';
import { AdComponent } from './src/components/AdComponent';
import { GeneratedLink } from './src/types/generatedLink';

import AboutUs from './src/pages/AboutUs';
import SavedLinks from './src/pages/SavedLinks';

const Stack = createStackNavigator();

function MainScreen({ navigation, route }: { navigation: any; route?: any }) {
	const [phoneNumber, setPhoneNumber] = useState('');
	const [isValidNumber, setIsValidNumber] = useState(false);
	const [menuVisible, setMenuVisible] = useState(false);
	const [message, setMessage] = useState('');

	const handlePhoneChange = (number: string, isValid: boolean) => {
		setPhoneNumber(number);
		setIsValidNumber(isValid);
	};

	const handleMessageChange = (messageText: string) => {
		setMessage(messageText);
	};

	// Handle reuse data from SavedLinks page
	useEffect(() => {
		if (route?.params?.reuseData) {
			const { phoneNumber: reusePhone, message: reuseMessage } = route.params.reuseData;
			setPhoneNumber(reusePhone);
			setMessage(reuseMessage);
			// Clear the params to prevent re-triggering
			navigation.setParams({ reuseData: undefined });
		}
	}, [route?.params?.reuseData, navigation]);

	const handleNavigateToSavedLinks = () => {
		navigation.navigate('SavedLinks');
	};

	const handleReuseLink = (link: GeneratedLink) => {
		setPhoneNumber(link.phoneNumber);
		setMessage(link.message);
	};

	return (
		<View style={styles.container}>


			<Modal
				animationType="fade"
				transparent={true}
				visible={menuVisible}
				onRequestClose={() => setMenuVisible(false)}
			>
				<TouchableOpacity
					style={styles.modalOverlay}
					activeOpacity={1}
					onPress={() => setMenuVisible(false)}
				>
					<View style={styles.menuContainer}>
						<TouchableOpacity
							style={styles.menuItem}
							onPress={() => {
								Linking.openURL(Platform.select({
									ios: 'https://apps.apple.com/app/your-app-id',
									android: 'market://details?id=com.quickchat.plustech'
								}) || 'market://details?id=com.quickchat.plustech');
								setMenuVisible(false);
							}}
						>
							<Text style={styles.menuItemText}>Rate App</Text>
						</TouchableOpacity>
						<View style={styles.menuDivider} />
						<TouchableOpacity
							style={styles.menuItem}
							onPress={() => {
								navigation.navigate('SavedLinks');
								setMenuVisible(false);
							}}
						>
							<Text style={styles.menuItemText}>📎 Saved Links</Text>
						</TouchableOpacity>
						<View style={styles.menuDivider} />
						<TouchableOpacity
							style={styles.menuItem}
							onPress={() => {
								navigation.navigate('AboutUs');
								setMenuVisible(false);
							}}
						>
							<Text style={styles.menuItemText}>About Us</Text>
						</TouchableOpacity>
					</View>
				</TouchableOpacity>
			</Modal>
			<ScrollView
				contentContainerStyle={styles.scrollContent}
				keyboardShouldPersistTaps="handled"
			>
				<TouchableOpacity
					style={styles.menuButton}
					onPress={() => setMenuVisible(true)}
				>
					<Text style={styles.menuButtonText}>☰</Text>
				</TouchableOpacity>
				<View style={styles.content}>
					<Text style={styles.title}>WALink</Text>
					<Text style={styles.subtitle}>Generate and manage WhatsApp links</Text>

					<View style={styles.card}>
						<PhoneNumberInput onChange={handlePhoneChange} />
						<View style={styles.divider} />
						<MessageInput
							onChange={handleMessageChange}
							phoneNumber={phoneNumber.replace(/[^0-9]/g, '')}
							initialMessage={message}
						/>
					</View>

					<RecentLinks
						onNavigateToSavedLinks={handleNavigateToSavedLinks}
						onReuseLink={handleReuseLink}
					/>
				</View>
				<AdComponent
					nativeId="ca-app-pub-2044352253676532/1240142088"
					bannerId="ca-app-pub-2044352253676532/5128611740"
					backgroundColor="#F0F8F5"
					showAdMedia={true}
				/>
			</ScrollView>

		</View>
	);
}

export default function App() {
	useEffect(() => {
		MobileAds()
			.initialize()
			.then(() => {
				console.log('AdMob initialized');
			})
			.catch((error) => {
				console.error('AdMob initialization error:', error);
			});
	}, []);

	return (
		<NavigationContainer>
			<Stack.Navigator>
				<Stack.Screen
					name="Main"
					component={MainScreen}
					options={{ headerShown: false }}
				/>
				<Stack.Screen
					name="SavedLinks"
					component={SavedLinks}
					options={{
						title: 'Saved Links',
						headerStyle: {
							backgroundColor: '#F0F8F5',
						},
						headerTintColor: '#128C7E',
						headerTitleStyle: {
							fontWeight: 'bold',
						},
					}}
				/>
				<Stack.Screen
					name="AboutUs"
					component={AboutUs}
					options={{
						title: 'About Us',
						headerStyle: {
							backgroundColor: '#F0F8F5',
						},
						headerTintColor: '#128C7E',
						headerTitleStyle: {
							fontWeight: 'bold',
						},
					}}
				/>
			</Stack.Navigator>
		</NavigationContainer>
	);
}

const styles = StyleSheet.create({
	menuButton: {
		position: 'absolute',
		top: Platform.OS === 'ios' ? 50 : 30,
		right: 20,
		zIndex: 1,
		padding: 10,
	},
	menuButtonText: {
		fontSize: 24,
		color: '#2D3748',
	},
	modalOverlay: {
		flex: 1,
		backgroundColor: 'rgba(0, 0, 0, 0.5)',
		justifyContent: 'flex-start',
		alignItems: 'flex-end',
	},
	menuContainer: {
		backgroundColor: 'white',
		borderRadius: 12,
		padding: 8,
		marginTop: Platform.OS === 'ios' ? 100 : 80,
		marginRight: 20,
		minWidth: 150,
		...Platform.select({
			ios: {
				shadowColor: '#000',
				shadowOffset: { width: 0, height: 2 },
				shadowOpacity: 0.25,
				shadowRadius: 4,
			},
			android: {
				elevation: 5,
			},
		}),
	},
	menuItem: {
		padding: 12,
	},
	menuItemText: {
		fontSize: 16,
		color: '#2D3748',
	},
	menuDivider: {
		height: 1,
		backgroundColor: '#E2E8F0',
	},
	container: {
		flex: 1,
		backgroundColor: '#F0F8F5',
	},
	scrollContent: {
		flexGrow: 1,
		padding: 24,
		paddingTop: Platform.OS === 'ios' ? 60 : 40,
		paddingBottom: 32,
	},
	content: {
		flex: 1,
		maxWidth: 600,
		width: '100%',
		alignSelf: 'center',
		marginBottom: 24,
	},
	card: {
		backgroundColor: '#ffffff',
		borderRadius: 20,
		padding: 24,
		marginTop: 20,
		borderWidth: 1,
		borderColor: '#E8F5E8',
		...Platform.select({
			ios: {
				shadowColor: '#25D366',
				shadowOffset: { width: 0, height: 4 },
				shadowOpacity: 0.08,
				shadowRadius: 16,
			},
			android: {
				elevation: 6,
			},
		}),
	},
	title: {
		fontSize: 42,
		fontWeight: '900',
		marginBottom: 12,
		textAlign: 'center',
		color: '#128C7E',
		letterSpacing: -1.5,
	},
	subtitle: {
		fontSize: 16,
		color: '#4A5568',
		textAlign: 'center',
		marginBottom: 32,
		lineHeight: 24,
		fontWeight: '500',
	},
	divider: {
		height: 1,
		backgroundColor: '#E2E8F0',
		marginVertical: 28,
	},
	buttonContainer: {
		marginTop: 24,
		gap: 16,
	},
	button: {
		padding: 16,
		borderRadius: 16,
		alignItems: 'center',
		justifyContent: 'center',
		shadowColor: '#000',
		shadowOffset: { width: 0, height: 2 },
		shadowOpacity: 0.1,
		shadowRadius: 4,
		elevation: 2,
	},
	whatsappButton: {
		backgroundColor: '#25D366',
	},
	lineButton: {
		backgroundColor: '#00B900',
	},
	buttonText: {
		color: 'white',
		fontWeight: '600',
		fontSize: 16,
		textTransform: 'uppercase',
		letterSpacing: 1,
	},
	disabledButton: {
		opacity: 0.6,
		transform: [{ scale: 0.98 }],
	},
	bannerContainer: {
		alignItems: 'center',
		paddingVertical: 10,
		backgroundColor: '#F0F8F5',
	},
});
