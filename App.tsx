import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, Platform, ScrollView, TouchableOpacity, Modal, Linking } from 'react-native';
import { MobileAds } from 'react-native-google-mobile-ads';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import PhoneNumberInput from './src/components/PhoneInput/index';
import MessageInput from './src/components/MessageInput/index';
import { AdComponent } from './src/components/AdComponent';

import AboutUs from './src/pages/AboutUs';

const Stack = createStackNavigator();

function MainScreen({ navigation }: { navigation: any }) {
	const [phoneNumber, setPhoneNumber] = useState('');
	const [isValidNumber, setIsValidNumber] = useState(false);
	const [menuVisible, setMenuVisible] = useState(false);

	const handlePhoneChange = (number: string, isValid: boolean) => {
		setPhoneNumber(number);
		setIsValidNumber(isValid);
	};

	return (
		<View style={styles.container}>


			<Modal
				animationType="fade"
				transparent={true}
				visible={menuVisible}
				onRequestClose={() => setMenuVisible(false)}
			>
				<TouchableOpacity
					style={styles.modalOverlay}
					activeOpacity={1}
					onPress={() => setMenuVisible(false)}
				>
					<View style={styles.menuContainer}>
						<TouchableOpacity
							style={styles.menuItem}
							onPress={() => {
								Linking.openURL(Platform.select({
									ios: 'https://apps.apple.com/app/your-app-id',
									android: 'market://details?id=com.quickchat.plustech'
								}) || 'market://details?id=com.quickchat.plustech');
								setMenuVisible(false);
							}}
						>
							<Text style={styles.menuItemText}>Rate App</Text>
						</TouchableOpacity>
						<View style={styles.menuDivider} />
						<TouchableOpacity
							style={styles.menuItem}
							onPress={() => {
								navigation.navigate('AboutUs');
								setMenuVisible(false);
							}}
						>
							<Text style={styles.menuItemText}>About Us</Text>
						</TouchableOpacity>
					</View>
				</TouchableOpacity>
			</Modal>
			<ScrollView
				contentContainerStyle={styles.scrollContent}
				keyboardShouldPersistTaps="handled"
			>
				<TouchableOpacity
					style={styles.menuButton}
					onPress={() => setMenuVisible(true)}
				>
					<Text style={styles.menuButtonText}>☰</Text>
				</TouchableOpacity>
				<View style={styles.content}>
					<Text style={styles.title}>Quick Chat</Text>
					<Text style={styles.subtitle}>Chat instantly via your WhatsApp</Text>

					<View style={styles.card}>
						<PhoneNumberInput onChange={handlePhoneChange} />
						<View style={styles.divider} />
						<MessageInput
							onChange={() => { }}
							phoneNumber={phoneNumber.replace(/[^0-9]/g, '')}
						/>
					</View>
				</View>
				<AdComponent
					nativeId="ca-app-pub-2044352253676532/1240142088"
					bannerId="ca-app-pub-2044352253676532/5128611740"
					backgroundColor="#F5F7FF"
					showAdMedia={true}
				/>
			</ScrollView>

		</View>
	);
}

export default function App() {
	useEffect(() => {
		MobileAds()
			.initialize()
			.then(() => {
				console.log('AdMob initialized');
			})
			.catch((error) => {
				console.error('AdMob initialization error:', error);
			});
	}, []);

	return (
		<NavigationContainer>
			<Stack.Navigator>
				<Stack.Screen
					name="Main"
					component={MainScreen}
					options={{ headerShown: false }}
				/>
				<Stack.Screen
					name="AboutUs"
					component={AboutUs}
					options={{
						title: 'About Us',
						headerStyle: {
							backgroundColor: '#F5F7FF',
						},
						headerTintColor: '#2D3748',
						headerTitleStyle: {
							fontWeight: 'bold',
						},
					}}
				/>
			</Stack.Navigator>
		</NavigationContainer>
	);
}

const styles = StyleSheet.create({
	menuButton: {
		position: 'absolute',
		top: Platform.OS === 'ios' ? 50 : 30,
		right: 20,
		zIndex: 1,
		padding: 10,
	},
	menuButtonText: {
		fontSize: 24,
		color: '#2D3748',
	},
	modalOverlay: {
		flex: 1,
		backgroundColor: 'rgba(0, 0, 0, 0.5)',
		justifyContent: 'flex-start',
		alignItems: 'flex-end',
	},
	menuContainer: {
		backgroundColor: 'white',
		borderRadius: 12,
		padding: 8,
		marginTop: Platform.OS === 'ios' ? 100 : 80,
		marginRight: 20,
		minWidth: 150,
		...Platform.select({
			ios: {
				shadowColor: '#000',
				shadowOffset: { width: 0, height: 2 },
				shadowOpacity: 0.25,
				shadowRadius: 4,
			},
			android: {
				elevation: 5,
			},
		}),
	},
	menuItem: {
		padding: 12,
	},
	menuItemText: {
		fontSize: 16,
		color: '#2D3748',
	},
	menuDivider: {
		height: 1,
		backgroundColor: '#E2E8F0',
	},
	container: {
		flex: 1,
		backgroundColor: '#F5F7FF',
	},
	scrollContent: {
		flexGrow: 1,
		padding: 24,
		paddingTop: Platform.OS === 'ios' ? 60 : 40,
		paddingBottom: 32,
	},
	content: {
		flex: 1,
		maxWidth: 600,
		width: '100%',
		alignSelf: 'center',
		marginBottom: 24,
	},
	card: {
		backgroundColor: '#ffffff',
		borderRadius: 24,
		padding: 28,
		marginTop: 24,
		...Platform.select({
			ios: {
				shadowColor: '#2D3748',
				shadowOffset: { width: 0, height: 4 },
				shadowOpacity: 0.1,
				shadowRadius: 12,
			},
			android: {
				elevation: 8,
			},
		}),
	},
	title: {
		fontSize: 40,
		fontWeight: '800',
		marginBottom: 16,
		textAlign: 'center',
		color: '#2D3748',
		letterSpacing: -1,
	},
	subtitle: {
		fontSize: 18,
		color: '#718096',
		textAlign: 'center',
		marginBottom: 36,
		lineHeight: 26,
	},
	divider: {
		height: 1,
		backgroundColor: '#E2E8F0',
		marginVertical: 28,
	},
	buttonContainer: {
		marginTop: 24,
		gap: 16,
	},
	button: {
		padding: 16,
		borderRadius: 16,
		alignItems: 'center',
		justifyContent: 'center',
		shadowColor: '#000',
		shadowOffset: { width: 0, height: 2 },
		shadowOpacity: 0.1,
		shadowRadius: 4,
		elevation: 2,
	},
	whatsappButton: {
		backgroundColor: '#25D366',
	},
	lineButton: {
		backgroundColor: '#00B900',
	},
	buttonText: {
		color: 'white',
		fontWeight: '600',
		fontSize: 16,
		textTransform: 'uppercase',
		letterSpacing: 1,
	},
	disabledButton: {
		opacity: 0.6,
		transform: [{ scale: 0.98 }],
	},
	bannerContainer: {
		alignItems: 'center',
		paddingVertical: 10,
		backgroundColor: '#F5F7FF',
	},
});
