# QuickChat

A React Native app for quick messaging connections

## Features

- Connect via WhatsApp with phone numbers
- Smart phone number input with country code detection
- Support for custom country codes not in the standard list
- Automatic country code recognition when pasting numbers
- Robust phone number validation and formatting
- Clipboard paste functionality with intelligent parsing
- Easy switching between country list and custom code input
- Message input for WhatsApp with pre-filled messages
- Cross-platform support (iOS and Android)
- Modern, user-friendly interface
- AdMob banner ads integration

## Bug Fixes

- Fixed clipboard paste functionality in custom code mode
  - Now preserves custom country code when pasting numbers
  - No longer switches to country list view unexpectedly
  - Maintains proper phone number formatting
- Fixed backspace behavior in custom code mode
  - No longer switches to country list when backspacing empty custom code input
  - Maintains proper state when backspacing through phone number

## Premium Features (Proposed)

### Communication Enhancement
- Bulk messaging: Send the same message to multiple contacts at once
- Message templates: Save and reuse frequently sent messages
- Rich text formatting: Add bold, italic, and other formatting to messages
- Scheduled messages: Schedule messages to be sent at specific times
- Contact groups: Create and manage groups of contacts

### Productivity Tools
- Contact history: Track your messaging history with each contact
- Quick replies: Create custom quick reply templates
- Contact notes: Add private notes to contacts
- Contact tags: Organize contacts with custom tags
- Export chat history: Export your messaging history to PDF/CSV

### Advanced Features
- Business hours: Set your availability hours for automated responses
- Analytics dashboard: View messaging statistics and patterns
- Custom themes: Choose from various app themes
- Ad-free experience: Remove all advertisements
- Priority support: Get faster support responses

### Integration Extensions
- Telegram integration: Connect with Telegram contacts
- Facebook Messenger integration: Connect with Messenger contacts
- Multiple accounts: Support for multiple WhatsApp
- Cloud backup: Backup your settings and templates
- Cross-device sync: Sync your data across multiple devices

## Development Setup

1. Clone the repository
2. Install dependencies: `npm install`
3. Run on iOS: `npx react-native run-ios`
4. Run on Android: `npx react-native run-android`

## Release Build

To create a signed release build for Android:

1. Ensure you have the keystore file (clone01.keystore) in android/app/
2. Set up the signing credentials in android/app/build.gradle
3. Create release build:
   ```bash
   cd android
   ./gradlew assembleRelease
   ```
4. The signed APK will be generated at:
   `android/app/build/outputs/apk/release/app-release.apk`

For iOS release builds, follow the standard React Native iOS deployment process.

## Design Features

- Soft, modern color palette using neutral tones
- Subtle shadows and rounded corners for depth and approachability
- Consistent spacing and padding for visual harmony
- Larger, more readable text inputs
- Clear visual hierarchy with proper contrast
- Responsive design that works across devices
