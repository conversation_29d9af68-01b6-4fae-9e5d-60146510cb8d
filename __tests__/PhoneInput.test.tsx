import React from 'react';
import { render, fireEvent, act } from '@testing-library/react-native';
import PhoneInput from '../src/components/PhoneInput';
import Clipboard from '@react-native-clipboard/clipboard';
import MessageInput from '../src/components/MessageInput';

// Mock clipboard
jest.mock('@react-native-clipboard/clipboard', () => ({
  getString: jest.fn()
}));

describe('PhoneInput component', () => {
  const onChangeMock = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  const testCases = [
    { 
      input: '+201115781509',
      expected: '+201115781509'
    },
    { 
      input: '201115781509',
      expected: '+1201115781509'
    },
    { 
      input: '01115781509',
      expected: '+101115781509'
    },{ 
      input: '00201115781509',
      expected: '+201115781509'
    }
  ];

  testCases.forEach(({ input, expected }) => {
    test(`should handle pasted phone number: ${input}`, async () => {
      // Mock clipboard content
      (Clipboard.getString as jest.Mock).mockResolvedValue(input);

      const { getByText } = render(
        <PhoneInput 
          onChange={onChangeMock}
        />
      );

      // Find and press the paste button
      const pasteButton = getByText('Paste');
      await act(async () => {
        await fireEvent.press(pasteButton);
      });

      // Wait for state updates to complete
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 0));
      });

      // The last call to onChange should have the expected values
      const lastCall = onChangeMock.mock.calls[onChangeMock.mock.calls.length - 1];
      expect(lastCall[0].replace(/\s+/g, '')).toEqual(expected);
    });
  });

  test('should handle manual phone number input', async () => {
    const { getByPlaceholderText } = render(
      <PhoneInput 
        onChange={onChangeMock}
      />
    );

    const phoneInput = getByPlaceholderText('Phone number');
    await act(async () => {
      fireEvent.changeText(phoneInput, '1234567890');
    });

    // Wait for state updates to complete
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 500));
    });

    // The last call to onChange should have the expected values
    const lastCall = onChangeMock.mock.calls[onChangeMock.mock.calls.length - 1];
    expect(lastCall[0].replace(/\s+/g, '')).toEqual('+11234567890');
  });

  test('should handle invalid phone numbers', async () => {
    const { getByPlaceholderText } = render(
      <PhoneInput 
        onChange={onChangeMock}
      />
    );

    const phoneInput = getByPlaceholderText('Phone number');
    await act(async () => {
      fireEvent.changeText(phoneInput, '12345'); // Too short to be valid
    });

    // Wait for state updates to complete
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    // The last call to onChange should have the expected values
    const lastCall = onChangeMock.mock.calls[onChangeMock.mock.calls.length - 1];
    
    expect(lastCall[0].replace(/\s+/g, '')).toEqual('+112345');
  });

  
});
