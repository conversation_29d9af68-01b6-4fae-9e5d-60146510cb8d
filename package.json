{"name": "QuickChat", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "release": "react-native build-android --mode=release", "test-release": "react-native run-android --mode=release", "brt": "npm i && npm run release && npm run test-release", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-native-clipboard/clipboard": "^1.16.0", "@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.2.0", "@react-navigation/stack": "^7.4.2", "@types/react-native-share": "^3.3.8", "react": "18.3.1", "react-native": "0.76.5", "react-native-device-country": "^1.0.5", "react-native-gesture-handler": "^2.27.1", "react-native-google-mobile-ads": "^14.8.0", "react-native-iap": "^13.0.3", "react-native-international-phone-number": "^0.8.5", "react-native-safe-area-context": "^5.5.1", "react-native-screens": "^4.11.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.76.5", "@react-native/eslint-config": "0.76.5", "@react-native/metro-config": "0.76.5", "@react-native/typescript-config": "0.76.5", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.0.0", "@types/jest": "^29.5.14", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "jest-environment-jsdom": "^29.7.0", "prettier": "2.8.8", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}