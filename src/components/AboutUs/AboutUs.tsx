import React from 'react';
import {Linking, StyleSheet, Text, TouchableOpacity, View, Platform} from 'react-native';

export default function AboutUs() {
  const Rate = (rate: number) => {

    if (rate === 5) {
     const storeUrl = 'market://details?id=com.quickchat.plustech';
        Linking.openURL(storeUrl).catch(() => {
          Linking.openURL('https://play.google.com/store/apps/details?id=com.quickchat.plustech');
        });
    }
  };
  return (
    <View style={styles.container}>
      <Text style={styles.rateUsTxt}>
        This application is under development by a single developer
      </Text>
      <Text style={styles.rateUsTxt}>
        Help us reach more people, Rate us 5 stars
      </Text>
      <TouchableOpacity
        style={styles.rateButton}
        onPress={() => Rate(5)}
      >
        <Text style={styles.rateButtonText}>Rate 5 Stars</Text>
      </TouchableOpacity>
    </View>
  );
}
const styles = StyleSheet.create({
  container: {
    padding: 28,
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
    width: '100%',
    marginHorizontal: 16,
    ...Platform.select({
      ios: {
        shadowColor: '#2D3748',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 12,
      },
      android: {
        elevation: 8,
      },
    }),
  },
  rateUsTxt: {
    fontSize: 18,
    color: '#718096',
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 26,
    letterSpacing: 0,
  },
  rateButton: {
    backgroundColor: '#25D366',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderRadius: 16,
    marginTop: 24,
    width: '100%',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  rateButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
});
