import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
} from 'react-native';
import { CreditService } from '../../services/creditService';
import { CreditBalance as CreditBalanceType } from '../../types/credits';

interface CreditBalanceProps {
  onPress?: () => void;
  refreshTrigger?: number; // Used to trigger refresh from parent
}

const CreditBalance: React.FC<CreditBalanceProps> = ({ onPress, refreshTrigger }) => {
  const [balance, setBalance] = useState<CreditBalanceType | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadBalance();
  }, [refreshTrigger]);

  const loadBalance = async () => {
    try {
      setLoading(true);
      const creditBalance = await CreditService.getCreditBalance();
      setBalance(creditBalance);
    } catch (error) {
      console.error('Error loading credit balance:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePress = () => {
    if (onPress) {
      onPress();
    }
  };

  if (loading || !balance) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>💎</Text>
        </View>
      </View>
    );
  }

  return (
    <TouchableOpacity
      style={[
        styles.container,
        balance.credits === 0 && styles.emptyContainer,
        onPress && styles.pressable
      ]}
      onPress={handlePress}
      disabled={!onPress}
      activeOpacity={onPress ? 0.7 : 1}
    >
      <View style={styles.content}>
        <Text style={styles.icon}>💎</Text>
        <Text style={[
          styles.creditText,
          balance.credits === 0 && styles.emptyCreditText
        ]}>
          {balance.credits}
        </Text>
      </View>
      {balance.credits === 0 && (
        <View style={styles.emptyIndicator}>
          <Text style={styles.emptyDot}>!</Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: '#E8F5E8',
    minWidth: 70,
    ...Platform.select({
      ios: {
        shadowColor: '#25D366',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  emptyContainer: {
    borderColor: '#FED7D7',
    backgroundColor: '#FFF5F5',
  },
  pressable: {
    // Add visual feedback for pressable state
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 6,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 24,
  },
  loadingText: {
    fontSize: 16,
    opacity: 0.5,
  },
  icon: {
    fontSize: 16,
  },
  creditText: {
    fontSize: 14,
    fontWeight: '700',
    color: '#128C7E',
  },
  emptyCreditText: {
    color: '#E53E3E',
  },
  emptyIndicator: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: '#E53E3E',
    borderRadius: 8,
    width: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyDot: {
    color: '#ffffff',
    fontSize: 10,
    fontWeight: '700',
  },
});

export default CreditBalance;
