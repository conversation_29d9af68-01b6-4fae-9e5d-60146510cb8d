import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Platform,
} from 'react-native';
import { CreditService } from '../../services/creditService';
import { CreditBalance } from '../../types/credits';

interface MonetizationModalProps {
  visible: boolean;
  onClose: () => void;
  onWatchAd: () => Promise<boolean>;
  onPurchaseCredits: () => Promise<boolean>;
  onCreditsUpdated?: (balance: CreditBalance) => void;
}

const MonetizationModal: React.FC<MonetizationModalProps> = ({
  visible,
  onClose,
  onWatchAd,
  onPurchaseCredits,
  onCreditsUpdated
}) => {
  const [creditBalance, setCreditBalance] = useState<CreditBalance | null>(null);
  const [isWatchingAd, setIsWatchingAd] = useState(false);
  const [isPurchasing, setIsPurchasing] = useState(false);
  const [remainingAds, setRemainingAds] = useState(0);

  useEffect(() => {
    if (visible) {
      loadCreditInfo();
    }
  }, [visible]);

  const loadCreditInfo = async () => {
    try {
      const balance = await CreditService.getCreditBalance();
      const remaining = await CreditService.getRemainingDailyAds();
      setCreditBalance(balance);
      setRemainingAds(remaining);
    } catch (error) {
      console.error('Error loading credit info:', error);
    }
  };

  const handleWatchAd = async () => {
    if (remainingAds <= 0) {
      Alert.alert(
        'Daily Limit Reached',
        'You have reached the daily limit for watching ads. Try again tomorrow or purchase credits.'
      );
      return;
    }

    setIsWatchingAd(true);
    try {
      const success = await onWatchAd();
      if (success) {
        const updatedBalance = await CreditService.getCreditBalance();
        setCreditBalance(updatedBalance);
        setRemainingAds(prev => Math.max(0, prev - 1));
        onCreditsUpdated?.(updatedBalance);
        
        Alert.alert(
          'Credits Earned!',
          'You earned 1 credit for watching the ad. Thank you!',
          [{ text: 'Continue', onPress: onClose }]
        );
      }
    } catch (error) {
      console.error('Error watching ad:', error);
      Alert.alert('Error', 'Failed to load ad. Please try again.');
    } finally {
      setIsWatchingAd(false);
    }
  };

  const handlePurchaseCredits = async () => {
    setIsPurchasing(true);
    try {
      const success = await onPurchaseCredits();
      if (success) {
        const updatedBalance = await CreditService.getCreditBalance();
        setCreditBalance(updatedBalance);
        onCreditsUpdated?.(updatedBalance);
        onClose();
      }
    } catch (error) {
      console.error('Error purchasing credits:', error);
      Alert.alert('Error', 'Failed to complete purchase. Please try again.');
    } finally {
      setIsPurchasing(false);
    }
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableOpacity
        style={styles.overlay}
        activeOpacity={1}
        onPress={onClose}
      >
        <TouchableOpacity
          style={styles.modalContainer}
          activeOpacity={1}
          onPress={(e) => e.stopPropagation()}
        >
          <View style={styles.header}>
            <Text style={styles.title}>Get Short Link Credits</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
            >
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.balanceContainer}>
            <Text style={styles.balanceLabel}>Current Balance</Text>
            <View style={styles.balanceDisplay}>
              <Text style={styles.balanceIcon}>💎</Text>
              <Text style={styles.balanceAmount}>
                {creditBalance?.credits || 0} Credits
              </Text>
            </View>
          </View>

          <Text style={styles.description}>
            You need credits to create shortened links. Choose an option below:
          </Text>

          <View style={styles.optionsContainer}>
            {/* Watch Ad Option */}
            <TouchableOpacity
              style={[
                styles.optionButton,
                styles.adButton,
                (remainingAds <= 0 || isWatchingAd) && styles.disabledButton
              ]}
              onPress={handleWatchAd}
              disabled={remainingAds <= 0 || isWatchingAd}
            >
              <View style={styles.optionContent}>
                <Text style={styles.optionIcon}>📺</Text>
                <View style={styles.optionTextContainer}>
                  <Text style={[styles.optionTitle, (remainingAds <= 0 || isWatchingAd) && styles.disabledText]}>
                    Watch Reward Ad
                  </Text>
                  <Text style={[styles.optionSubtitle, (remainingAds <= 0 || isWatchingAd) && styles.disabledText]}>
                    {remainingAds > 0 
                      ? `Earn 1 credit • ${remainingAds} ads left today`
                      : 'Daily limit reached'
                    }
                  </Text>
                </View>
                {isWatchingAd && (
                  <ActivityIndicator size="small" color="#25D366" />
                )}
              </View>
            </TouchableOpacity>

            {/* Purchase Option */}
            <TouchableOpacity
              style={[styles.optionButton, styles.purchaseButton, isPurchasing && styles.disabledButton]}
              onPress={handlePurchaseCredits}
              disabled={isPurchasing}
            >
              <View style={styles.optionContent}>
                <Text style={styles.optionIcon}>💳</Text>
                <View style={styles.optionTextContainer}>
                  <Text style={[styles.optionTitle, isPurchasing && styles.disabledText]}>
                    Purchase Credits
                  </Text>
                  <Text style={[styles.optionSubtitle, isPurchasing && styles.disabledText]}>
                    Get 3 credits for $0.99
                  </Text>
                </View>
                {isPurchasing && (
                  <ActivityIndicator size="small" color="#ffffff" />
                )}
              </View>
            </TouchableOpacity>
          </View>

          <View style={styles.infoContainer}>
            <Text style={styles.infoText}>
              💡 Credits never expire and can be used anytime
            </Text>
          </View>
        </TouchableOpacity>
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 20,
    padding: 24,
    width: '100%',
    maxWidth: 400,
    borderWidth: 1,
    borderColor: '#E8F5E8',
    ...Platform.select({
      ios: {
        shadowColor: '#25D366',
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.15,
        shadowRadius: 20,
      },
      android: {
        elevation: 10,
      },
    }),
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    color: '#2D3748',
    flex: 1,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F7FAFC',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 16,
    color: '#718096',
    fontWeight: '600',
  },
  balanceContainer: {
    backgroundColor: '#F0F8F5',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#E8F5E8',
  },
  balanceLabel: {
    fontSize: 12,
    color: '#718096',
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    marginBottom: 8,
  },
  balanceDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  balanceIcon: {
    fontSize: 20,
  },
  balanceAmount: {
    fontSize: 18,
    fontWeight: '700',
    color: '#128C7E',
  },
  description: {
    fontSize: 14,
    color: '#4A5568',
    lineHeight: 20,
    marginBottom: 24,
    textAlign: 'center',
  },
  optionsContainer: {
    gap: 12,
    marginBottom: 20,
  },
  optionButton: {
    borderRadius: 16,
    padding: 16,
    borderWidth: 2,
  },
  adButton: {
    backgroundColor: '#F0F8F5',
    borderColor: '#25D366',
  },
  purchaseButton: {
    backgroundColor: '#25D366',
    borderColor: '#25D366',
  },
  disabledButton: {
    opacity: 0.5,
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  optionIcon: {
    fontSize: 24,
  },
  optionTextContainer: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2D3748',
    marginBottom: 2,
  },
  optionSubtitle: {
    fontSize: 12,
    color: '#718096',
    fontWeight: '500',
  },
  disabledText: {
    opacity: 0.6,
  },
  infoContainer: {
    backgroundColor: '#F7FAFC',
    borderRadius: 8,
    padding: 12,
  },
  infoText: {
    fontSize: 12,
    color: '#4A5568',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default MonetizationModal;
