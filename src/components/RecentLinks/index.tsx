import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Platform,
} from 'react-native';
import { GeneratedLink } from '../../types/generatedLink';
import { GeneratedLinkService } from '../../services/generatedLinkService';

interface RecentLinksProps {
  onNavigateToSavedLinks: () => void;
  onReuseLink: (link: GeneratedLink) => void;
}

const RecentLinks: React.FC<RecentLinksProps> = ({ onNavigateToSavedLinks, onReuseLink }) => {
  const [recentLinks, setRecentLinks] = useState<GeneratedLink[]>([]);
  const [totalCount, setTotalCount] = useState(0);

  useEffect(() => {
    loadRecentLinks();
  }, []);

  const loadRecentLinks = async () => {
    try {
      const recent = await GeneratedLinkService.getRecentLinks(2);
      const all = await GeneratedLinkService.getAllLinks();
      setRecentLinks(recent);
      setTotalCount(all.length);
    } catch (error) {
      console.error('Error loading recent links:', error);
    }
  };

  const formatTimestamp = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  const formatPhoneNumber = (phoneNumber: string) => {
    if (phoneNumber.startsWith('+')) {
      return phoneNumber;
    }
    return `+${phoneNumber}`;
  };

  if (totalCount === 0) {
    return null; // Don't show the section if no links exist
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Recent Links</Text>
        <TouchableOpacity
          style={styles.viewAllButton}
          onPress={onNavigateToSavedLinks}
        >
          <Text style={styles.viewAllText}>
            View All ({totalCount})
          </Text>
        </TouchableOpacity>
      </View>

      {recentLinks.length > 0 ? (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
        >
          {recentLinks.map((link) => (
            <TouchableOpacity
              key={link.id}
              style={styles.linkCard}
              onPress={() => onReuseLink(link)}
              activeOpacity={0.7}
            >
              <Text style={styles.linkLabel} numberOfLines={1}>
                {link.label}
              </Text>
              <Text style={styles.phoneNumber} numberOfLines={1}>
                {formatPhoneNumber(link.phoneNumber)}
              </Text>
              <Text style={styles.message} numberOfLines={2}>
                {link.message}
              </Text>
              <Text style={styles.timestamp}>
                {formatTimestamp(link.timestamp)}
              </Text>
              <View style={styles.urlIndicators}>
                <View style={styles.urlDot} />
                {link.shortenedUrl && <View style={[styles.urlDot, styles.shortenedDot]} />}
              </View>
            </TouchableOpacity>
          ))}
        </ScrollView>
      ) : (
        <Text style={styles.emptyText}>
          Generate your first link to see it here
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 20,
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2D3748',
  },
  viewAllButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#E8F5E8',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#25D366',
  },
  viewAllText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#128C7E',
  },
  scrollView: {
    flexGrow: 0,
  },
  scrollContent: {
    paddingRight: 16,
  },
  linkCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 12,
    marginRight: 12,
    width: 160,
    borderWidth: 1,
    borderColor: '#E8F5E8',
    ...Platform.select({
      ios: {
        shadowColor: '#25D366',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  linkLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2D3748',
    marginBottom: 4,
  },
  phoneNumber: {
    fontSize: 12,
    color: '#128C7E',
    fontWeight: '500',
    marginBottom: 4,
  },
  message: {
    fontSize: 11,
    color: '#4A5568',
    lineHeight: 16,
    marginBottom: 8,
    minHeight: 32,
  },
  timestamp: {
    fontSize: 10,
    color: '#718096',
    marginBottom: 6,
  },
  urlIndicators: {
    flexDirection: 'row',
    gap: 4,
  },
  urlDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#4299E1',
  },
  shortenedDot: {
    backgroundColor: '#25D366',
  },
  emptyText: {
    fontSize: 14,
    color: '#718096',
    textAlign: 'center',
    fontStyle: 'italic',
    paddingVertical: 20,
  },
});

export default RecentLinks;
