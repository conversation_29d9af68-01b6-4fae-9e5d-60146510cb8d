import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Platform,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  ScrollView,
} from 'react-native';
import { SavedMessage, SavedMessageInput } from '../../types/savedMessage';
import { SavedMessageService } from '../../services/savedMessageService';

interface SaveMessageModalProps {
  visible: boolean;
  onClose: () => void;
  messageContent: string;
  editingMessage?: SavedMessage | null;
  onSaveSuccess?: (message: SavedMessage) => void;
}

const SaveMessageModal: React.FC<SaveMessageModalProps> = ({
  visible,
  onClose,
  messageContent,
  editingMessage,
  onSaveSuccess,
}) => {
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [loading, setSaving] = useState(false);

  useEffect(() => {
    if (visible) {
      if (editingMessage) {
        // Editing existing message
        setTitle(editingMessage.title);
        setContent(editingMessage.content);
      } else {
        // Creating new message
        setTitle('');
        setContent(messageContent);
      }
    }
  }, [visible, messageContent, editingMessage]);

  const handleSave = async () => {
    const trimmedTitle = title.trim();
    const trimmedContent = content.trim();

    if (!trimmedTitle) {
      Alert.alert('Error', 'Please enter a title for your message');
      return;
    }

    if (!trimmedContent) {
      Alert.alert('Error', 'Message content cannot be empty');
      return;
    }

    setSaving(true);
    try {
      let savedMessage: SavedMessage;

      if (editingMessage) {
        // Update existing message
        const updated = await SavedMessageService.updateMessage(editingMessage.id, {
          title: trimmedTitle,
          content: trimmedContent,
        });
        if (!updated) {
          throw new Error('Failed to update message');
        }
        savedMessage = updated;
      } else {
        // Create new message
        const messageInput: SavedMessageInput = {
          title: trimmedTitle,
          content: trimmedContent,
        };
        savedMessage = await SavedMessageService.saveMessage(messageInput);
      }

      onSaveSuccess?.(savedMessage);
      onClose();
      
      Alert.alert(
        'Success',
        editingMessage ? 'Message updated successfully!' : 'Message saved successfully!',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error saving message:', error);
      Alert.alert(
        'Error',
        editingMessage ? 'Failed to update message' : 'Failed to save message'
      );
    } finally {
      setSaving(false);
    }
  };

  const handleClose = () => {
    setTitle('');
    setContent('');
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.header}>
          <Text style={styles.title}>
            {editingMessage ? 'Edit Message' : 'Save Message'}
          </Text>
          <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
            <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Title</Text>
            <TextInput
              style={styles.titleInput}
              placeholder="Enter a title for this message..."
              placeholderTextColor="#718096"
              value={title}
              onChangeText={setTitle}
              maxLength={50}
              autoFocus={!editingMessage}
            />
            <Text style={styles.characterCount}>{title.length}/50</Text>
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Message Content</Text>
            <TextInput
              style={styles.contentInput}
              placeholder="Your message content..."
              placeholderTextColor="#718096"
              value={content}
              onChangeText={setContent}
              multiline
              textAlignVertical="top"
              maxLength={1000}
            />
            <Text style={styles.characterCount}>{content.length}/1000</Text>
          </View>
        </ScrollView>

        <View style={styles.footer}>
          <TouchableOpacity
            style={[styles.button, styles.cancelButton]}
            onPress={handleClose}
            disabled={loading}
          >
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.saveButton]}
            onPress={handleSave}
            disabled={loading || !title.trim() || !content.trim()}
          >
            {loading ? (
              <ActivityIndicator size="small" color="#ffffff" />
            ) : (
              <Text style={styles.saveButtonText}>
                {editingMessage ? 'Update' : 'Save'}
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F7FF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#2D3748',
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#E2E8F0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 16,
    color: '#2D3748',
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  inputContainer: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2D3748',
    marginBottom: 8,
  },
  titleInput: {
    borderWidth: 1,
    borderColor: '#E2E8F0',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: '#2D3748',
    backgroundColor: '#ffffff',
    ...Platform.select({
      ios: {
        shadowColor: '#2D3748',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  contentInput: {
    borderWidth: 1,
    borderColor: '#E2E8F0',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: '#2D3748',
    backgroundColor: '#ffffff',
    minHeight: 120,
    ...Platform.select({
      ios: {
        shadowColor: '#2D3748',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  characterCount: {
    fontSize: 12,
    color: '#A0AEC0',
    textAlign: 'right',
    marginTop: 4,
  },
  footer: {
    flexDirection: 'row',
    padding: 20,
    paddingBottom: Platform.OS === 'ios' ? 40 : 20,
    backgroundColor: '#ffffff',
    borderTopWidth: 1,
    borderTopColor: '#E2E8F0',
  },
  button: {
    flex: 1,
    height: 48,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 8,
  },
  cancelButton: {
    backgroundColor: '#F7FAFC',
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#718096',
  },
  saveButton: {
    backgroundColor: '#25D366',
    ...Platform.select({
      ios: {
        shadowColor: '#1A8D3D',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
});

export default SaveMessageModal;
