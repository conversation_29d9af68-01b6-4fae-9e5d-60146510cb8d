import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  Alert,
  RefreshControl,
  Platform,
  Linking,
} from 'react-native';
import Clipboard from '@react-native-clipboard/clipboard';
import { GeneratedLink } from '../types/generatedLink';
import { GeneratedLinkService } from '../services/generatedLinkService';

interface SavedLinksProps {
  navigation: any;
  route?: {
    params?: {
      onSelectLink?: (link: GeneratedLink) => void;
    };
  };
}

const SavedLinks: React.FC<SavedLinksProps> = ({ navigation, route }) => {
  const [links, setLinks] = useState<GeneratedLink[]>([]);
  const [filteredLinks, setFilteredLinks] = useState<GeneratedLink[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);

  const loadLinks = useCallback(async () => {
    try {
      const allLinks = await GeneratedLinkService.getAllLinks();
      setLinks(allLinks);
      setFilteredLinks(allLinks);
    } catch (error) {
      console.error('Error loading links:', error);
      Alert.alert('Error', 'Failed to load saved links');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadLinks();
  }, [loadLinks]);

  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredLinks(links);
    } else {
      const filtered = links.filter(link =>
        link.label?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        link.phoneNumber.includes(searchQuery) ||
        link.message.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredLinks(filtered);
    }
  }, [searchQuery, links]);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadLinks();
    setRefreshing(false);
  }, [loadLinks]);

  const handleCopyOriginalUrl = async (link: GeneratedLink) => {
    try {
      await Clipboard.setString(link.originalUrl);
      Alert.alert('Success', 'Original URL copied to clipboard!');
    } catch (error) {
      Alert.alert('Error', 'Failed to copy URL');
    }
  };

  const handleCopyShortenedUrl = async (link: GeneratedLink) => {
    if (!link.shortenedUrl) {
      Alert.alert('Info', 'No shortened URL available for this link');
      return;
    }
    try {
      await Clipboard.setString(link.shortenedUrl);
      Alert.alert('Success', 'Shortened URL copied to clipboard!');
    } catch (error) {
      Alert.alert('Error', 'Failed to copy URL');
    }
  };

  const handleReuseLink = (link: GeneratedLink) => {
    if (route?.params?.onSelectLink) {
      route.params.onSelectLink(link);
      navigation.goBack();
    } else {
      // Navigate back to main screen with the link data
      navigation.navigate('Main', { 
        reuseData: {
          phoneNumber: link.phoneNumber,
          message: link.message
        }
      });
    }
  };

  const handleDeleteLink = async (link: GeneratedLink) => {
    Alert.alert(
      'Delete Link',
      'Are you sure you want to delete this link?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await GeneratedLinkService.deleteLink(link.id);
              await loadLinks();
              Alert.alert('Success', 'Link deleted successfully');
            } catch (error) {
              Alert.alert('Error', 'Failed to delete link');
            }
          },
        },
      ]
    );
  };

  const handleOpenInWhatsApp = async (link: GeneratedLink) => {
    try {
      const whatsappUrl = `whatsapp://send?phone=${link.phoneNumber.replace(/^\+/, '')}&text=${encodeURIComponent(link.message)}`;
      await Linking.openURL(whatsappUrl);
    } catch (error) {
      Alert.alert('Error', 'Unable to open WhatsApp');
    }
  };

  const formatTimestamp = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    return new Date(timestamp).toLocaleDateString();
  };

  const formatPhoneNumber = (phoneNumber: string) => {
    // Simple formatting for display
    if (phoneNumber.startsWith('+')) {
      return phoneNumber;
    }
    return `+${phoneNumber}`;
  };

  const renderLinkItem = ({ item }: { item: GeneratedLink }) => (
    <View style={styles.linkItem}>
      <View style={styles.linkHeader}>
        <Text style={styles.linkLabel} numberOfLines={1}>
          {item.label}
        </Text>
        <Text style={styles.timestamp}>
          {formatTimestamp(item.timestamp)}
        </Text>
      </View>
      
      <Text style={styles.phoneNumber}>
        {formatPhoneNumber(item.phoneNumber)}
      </Text>
      
      <Text style={styles.message} numberOfLines={2}>
        {item.message}
      </Text>
      
      <View style={styles.urlInfo}>
        <Text style={styles.urlLabel}>
          URLs: Original{item.shortenedUrl ? ' • Shortened' : ''}
        </Text>
      </View>
      
      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={[styles.actionButton, styles.copyButton]}
          onPress={() => handleCopyOriginalUrl(item)}
        >
          <Text style={styles.copyButtonText}>Copy</Text>
        </TouchableOpacity>

        {item.shortenedUrl && (
          <TouchableOpacity
            style={[styles.actionButton, styles.copyButton]}
            onPress={() => handleCopyShortenedUrl(item)}
          >
            <Text style={styles.copyButtonText}>Short</Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity
          style={[styles.actionButton, styles.reuseButton]}
          onPress={() => handleReuseLink(item)}
        >
          <Text style={styles.actionButtonText}>Use</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.actionButton, styles.whatsappButton]}
          onPress={() => handleOpenInWhatsApp(item)}
        >
          <Text style={styles.actionButtonText}>Open</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.actionButton, styles.deleteButton]}
          onPress={() => handleDeleteLink(item)}
        >
          <Text style={styles.actionButtonText}>Delete</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyStateTitle}>No Saved Links</Text>
      <Text style={styles.emptyStateText}>
        Generate WhatsApp links from the main screen to see them here
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholder="Search links..."
          placeholderTextColor="#718096"
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>
      
      <FlatList
        data={filteredLinks}
        renderItem={renderLinkItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor="#25D366"
            colors={['#25D366']}
          />
        }
        ListEmptyComponent={!loading ? renderEmptyState : null}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F0F8F5',
  },
  searchContainer: {
    padding: 16,
    paddingBottom: 8,
  },
  searchInput: {
    height: 44,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    paddingHorizontal: 16,
    fontSize: 16,
    color: '#2D3748',
    borderWidth: 1,
    borderColor: '#E8F5E8',
    ...Platform.select({
      ios: {
        shadowColor: '#25D366',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  listContainer: {
    padding: 16,
    paddingTop: 8,
  },
  linkItem: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E8F5E8',
    ...Platform.select({
      ios: {
        shadowColor: '#25D366',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 8,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  linkHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  linkLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2D3748',
    flex: 1,
    marginRight: 8,
  },
  timestamp: {
    fontSize: 12,
    color: '#718096',
    fontWeight: '500',
  },
  phoneNumber: {
    fontSize: 14,
    color: '#128C7E',
    fontWeight: '600',
    marginBottom: 4,
  },
  message: {
    fontSize: 14,
    color: '#4A5568',
    lineHeight: 20,
    marginBottom: 8,
  },
  urlInfo: {
    marginBottom: 12,
  },
  urlLabel: {
    fontSize: 12,
    color: '#718096',
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 36,
  },
  copyButton: {
    backgroundColor: '#EDF2F7',
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  reuseButton: {
    backgroundColor: '#4299E1',
  },
  whatsappButton: {
    backgroundColor: '#25D366',
  },
  deleteButton: {
    backgroundColor: '#E53E3E',
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#ffffff',
  },
  copyButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#4A5568',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#2D3748',
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#718096',
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: 32,
  },
});

export default SavedLinks;
