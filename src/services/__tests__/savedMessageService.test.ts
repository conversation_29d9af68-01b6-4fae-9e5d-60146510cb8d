import AsyncStorage from '@react-native-async-storage/async-storage';
import { SavedMessageService } from '../savedMessageService';
import { SavedMessageInput } from '../../types/savedMessage';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
}));

const mockAsyncStorage = AsyncStorage as jest.Mocked<typeof AsyncStorage>;

describe('SavedMessageService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getAllMessages', () => {
    it('should return empty array when no messages exist', async () => {
      mockAsyncStorage.getItem.mockResolvedValue(null);
      
      const messages = await SavedMessageService.getAllMessages();
      
      expect(messages).toEqual([]);
      expect(mockAsyncStorage.getItem).toHaveBeenCalledWith('QUICKCHAT_SAVED_MESSAGES');
    });

    it('should return parsed messages sorted by newest first', async () => {
      const mockMessages = [
        {
          id: 'msg_1',
          title: 'Test 1',
          content: 'Content 1',
          timestamp: 1000,
          createdAt: new Date(1000).toISOString(),
        },
        {
          id: 'msg_2',
          title: 'Test 2',
          content: 'Content 2',
          timestamp: 2000,
          createdAt: new Date(2000).toISOString(),
        },
      ];
      
      mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify(mockMessages));
      
      const messages = await SavedMessageService.getAllMessages();
      
      expect(messages).toHaveLength(2);
      expect(messages[0].timestamp).toBe(2000); // Newest first
      expect(messages[1].timestamp).toBe(1000);
    });

    it('should handle AsyncStorage errors gracefully', async () => {
      mockAsyncStorage.getItem.mockRejectedValue(new Error('Storage error'));
      
      const messages = await SavedMessageService.getAllMessages();
      
      expect(messages).toEqual([]);
    });
  });

  describe('saveMessage', () => {
    it('should save a new message successfully', async () => {
      mockAsyncStorage.getItem.mockResolvedValue('[]');
      mockAsyncStorage.setItem.mockResolvedValue();
      
      const input: SavedMessageInput = {
        title: 'Test Message',
        content: 'Test content',
      };
      
      const savedMessage = await SavedMessageService.saveMessage(input);
      
      expect(savedMessage.title).toBe('Test Message');
      expect(savedMessage.content).toBe('Test content');
      expect(savedMessage.id).toBeDefined();
      expect(savedMessage.timestamp).toBeDefined();
      expect(savedMessage.createdAt).toBeInstanceOf(Date);
      expect(mockAsyncStorage.setItem).toHaveBeenCalled();
    });

    it('should trim whitespace from title and content', async () => {
      mockAsyncStorage.getItem.mockResolvedValue('[]');
      mockAsyncStorage.setItem.mockResolvedValue();
      
      const input: SavedMessageInput = {
        title: '  Test Message  ',
        content: '  Test content  ',
      };
      
      const savedMessage = await SavedMessageService.saveMessage(input);
      
      expect(savedMessage.title).toBe('Test Message');
      expect(savedMessage.content).toBe('Test content');
    });

    it('should handle save errors', async () => {
      mockAsyncStorage.getItem.mockResolvedValue('[]');
      mockAsyncStorage.setItem.mockRejectedValue(new Error('Storage error'));

      const input: SavedMessageInput = {
        title: 'Test Message',
        content: 'Test content',
      };

      await expect(SavedMessageService.saveMessage(input)).rejects.toThrow('Failed to save message');
    });
  });

  describe('deleteMessage', () => {
    it('should delete an existing message', async () => {
      const mockMessages = [
        {
          id: 'msg_1',
          title: 'Test 1',
          content: 'Content 1',
          timestamp: 1000,
          createdAt: new Date(1000).toISOString(),
        },
        {
          id: 'msg_2',
          title: 'Test 2',
          content: 'Content 2',
          timestamp: 2000,
          createdAt: new Date(2000).toISOString(),
        },
      ];
      
      mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify(mockMessages));
      mockAsyncStorage.setItem.mockResolvedValue();
      
      const result = await SavedMessageService.deleteMessage('msg_1');
      
      expect(result).toBe(true);
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        'QUICKCHAT_SAVED_MESSAGES',
        expect.stringContaining('msg_2')
      );
    });

    it('should return false when message not found', async () => {
      mockAsyncStorage.getItem.mockResolvedValue('[]');
      
      const result = await SavedMessageService.deleteMessage('nonexistent');
      
      expect(result).toBe(false);
    });

    it('should handle delete errors', async () => {
      const mockMessages = [
        {
          id: 'msg_1',
          title: 'Test 1',
          content: 'Content 1',
          timestamp: 1000,
          createdAt: new Date(1000).toISOString(),
        },
      ];

      mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify(mockMessages));
      mockAsyncStorage.setItem.mockRejectedValue(new Error('Storage error'));

      await expect(SavedMessageService.deleteMessage('msg_1')).rejects.toThrow('Failed to delete message');
    });
  });

  describe('updateMessage', () => {
    it('should update an existing message', async () => {
      const mockMessages = [
        {
          id: 'msg_1',
          title: 'Test 1',
          content: 'Content 1',
          timestamp: 1000,
          createdAt: new Date(1000).toISOString(),
        },
      ];
      
      mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify(mockMessages));
      mockAsyncStorage.setItem.mockResolvedValue();
      
      const updatedMessage = await SavedMessageService.updateMessage('msg_1', {
        title: 'Updated Title',
        content: 'Updated Content',
      });
      
      expect(updatedMessage?.title).toBe('Updated Title');
      expect(updatedMessage?.content).toBe('Updated Content');
      expect(updatedMessage?.id).toBe('msg_1'); // ID should remain unchanged
      expect(updatedMessage?.timestamp).toBe(1000); // Timestamp should remain unchanged
    });

    it('should throw error when message not found', async () => {
      mockAsyncStorage.getItem.mockResolvedValue('[]');

      await expect(SavedMessageService.updateMessage('nonexistent', {
        title: 'Updated Title',
      })).rejects.toThrow('Failed to update message');
    });
  });

  describe('clearAllMessages', () => {
    it('should clear all messages', async () => {
      mockAsyncStorage.removeItem.mockResolvedValue();

      await SavedMessageService.clearAllMessages();

      expect(mockAsyncStorage.removeItem).toHaveBeenCalledWith('QUICKCHAT_SAVED_MESSAGES');
    });

    it('should handle clear errors', async () => {
      mockAsyncStorage.removeItem.mockRejectedValue(new Error('Storage error'));

      await expect(SavedMessageService.clearAllMessages()).rejects.toThrow('Failed to clear messages');
    });
  });

  describe('initializeDefaultMessages', () => {
    it('should create default "hi" message when no messages exist', async () => {
      mockAsyncStorage.getItem.mockResolvedValue(null);
      mockAsyncStorage.setItem.mockResolvedValue();

      await SavedMessageService.initializeDefaultMessages();

      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        'QUICKCHAT_SAVED_MESSAGES',
        expect.stringContaining('"title":"hi"')
      );
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        'QUICKCHAT_SAVED_MESSAGES',
        expect.stringContaining('"content":"hi"')
      );
    });

    it('should not create default message when messages already exist', async () => {
      const existingMessages = [
        {
          id: 'msg_1',
          title: 'Existing',
          content: 'Existing message',
          timestamp: 1000,
          createdAt: new Date(1000).toISOString(),
        },
      ];

      mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify(existingMessages));

      await SavedMessageService.initializeDefaultMessages();

      expect(mockAsyncStorage.setItem).not.toHaveBeenCalled();
    });

    it('should handle initialization errors gracefully', async () => {
      mockAsyncStorage.getItem.mockRejectedValue(new Error('Storage error'));

      // Should not throw error
      await expect(SavedMessageService.initializeDefaultMessages()).resolves.toBeUndefined();
    });
  });
});
