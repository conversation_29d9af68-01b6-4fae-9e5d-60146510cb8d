import { RewardedAd, RewardedAdEventType, TestIds } from 'react-native-google-mobile-ads';
import { CreditService } from './creditService';
import { CREDIT_CONSTANTS } from '../types/credits';

// Use test ad unit ID for development, replace with real ID for production
const REWARDED_AD_UNIT_ID = __DEV__ 
  ? TestIds.REWARDED 
  : 'ca-app-pub-2044352253676532/XXXXXXXX'; // Replace with your actual ad unit ID

export class AdService {
  private static rewardedAd: RewardedAd | null = null;
  private static isLoading = false;
  private static isLoaded = false;

  static async initialize(): Promise<void> {
    try {
      this.rewardedAd = RewardedAd.createForAdRequest(REWARDED_AD_UNIT_ID);
      this.setupAdEventListeners();
      await this.loadAd();
    } catch (error) {
      console.error('Error initializing ad service:', error);
    }
  }

  private static setupAdEventListeners(): void {
    if (!this.rewardedAd) return;

    this.rewardedAd.addAdEventListener(RewardedAdEventType.LOADED, () => {
      console.log('Rewarded ad loaded');
      this.isLoaded = true;
      this.isLoading = false;
    });

    this.rewardedAd.addAdEventListener(RewardedAdEventType.EARNED_REWARD, (reward) => {
      console.log('User earned reward:', reward);
      // The reward will be handled in the showAd method
    });

    this.rewardedAd.addAdEventListener(RewardedAdEventType.FAILED_TO_LOAD_EVENT, (error) => {
      console.error('Rewarded ad failed to load:', error);
      this.isLoaded = false;
      this.isLoading = false;
    });

    this.rewardedAd.addAdEventListener(RewardedAdEventType.OPENED, () => {
      console.log('Rewarded ad opened');
    });

    this.rewardedAd.addAdEventListener(RewardedAdEventType.CLOSED, () => {
      console.log('Rewarded ad closed');
      this.isLoaded = false;
      // Preload the next ad
      this.loadAd();
    });
  }

  private static async loadAd(): Promise<void> {
    if (!this.rewardedAd || this.isLoading || this.isLoaded) {
      return;
    }

    try {
      this.isLoading = true;
      await this.rewardedAd.load();
    } catch (error) {
      console.error('Error loading rewarded ad:', error);
      this.isLoading = false;
    }
  }

  static async isAdReady(): Promise<boolean> {
    try {
      return this.isLoaded && this.rewardedAd !== null;
    } catch (error) {
      console.error('Error checking ad readiness:', error);
      return false;
    }
  }

  static async canShowAd(): Promise<{
    canShow: boolean;
    reason?: string;
  }> {
    try {
      // Check if user can watch more ads today
      const canWatchAd = await CreditService.canWatchAd();
      if (!canWatchAd) {
        return {
          canShow: false,
          reason: 'Daily ad limit reached'
        };
      }

      // Check if ad is ready
      const adReady = await this.isAdReady();
      if (!adReady) {
        return {
          canShow: false,
          reason: 'Ad not ready'
        };
      }

      return { canShow: true };
    } catch (error) {
      console.error('Error checking if can show ad:', error);
      return {
        canShow: false,
        reason: 'Error checking ad availability'
      };
    }
  }

  static async showRewardedAd(): Promise<{
    success: boolean;
    rewarded: boolean;
    error?: string;
  }> {
    try {
      const { canShow, reason } = await this.canShowAd();
      if (!canShow) {
        return {
          success: false,
          rewarded: false,
          error: reason
        };
      }

      if (!this.rewardedAd) {
        return {
          success: false,
          rewarded: false,
          error: 'Ad not initialized'
        };
      }

      return new Promise((resolve) => {
        let rewardEarned = false;

        // Set up one-time listeners for this ad show
        const rewardListener = this.rewardedAd!.addAdEventListener(
          RewardedAdEventType.EARNED_REWARD,
          async (reward) => {
            console.log('Reward earned:', reward);
            rewardEarned = true;
            
            try {
              // Award credits to user
              await CreditService.addCredits(
                CREDIT_CONSTANTS.AD_REWARD_CREDITS,
                'ad',
                REWARDED_AD_UNIT_ID
              );
            } catch (error) {
              console.error('Error awarding credits:', error);
            }
          }
        );

        const closeListener = this.rewardedAd!.addAdEventListener(
          RewardedAdEventType.CLOSED,
          () => {
            // Clean up listeners
            rewardListener();
            closeListener();

            resolve({
              success: true,
              rewarded: rewardEarned,
              error: rewardEarned ? undefined : 'Ad closed without reward'
            });
          }
        );

        // Show the ad
        this.rewardedAd!.show();
        this.isLoaded = false;
      });

    } catch (error) {
      console.error('Error showing rewarded ad:', error);
      return {
        success: false,
        rewarded: false,
        error: error instanceof Error ? error.message : 'Failed to show ad'
      };
    }
  }

  static async preloadAd(): Promise<void> {
    try {
      if (!this.isLoaded && !this.isLoading) {
        await this.loadAd();
      }
    } catch (error) {
      console.error('Error preloading ad:', error);
    }
  }

  static async getAdStatus(): Promise<{
    isInitialized: boolean;
    isLoaded: boolean;
    isLoading: boolean;
    canWatchAd: boolean;
    remainingAds: number;
  }> {
    try {
      const canWatchAd = await CreditService.canWatchAd();
      const remainingAds = await CreditService.getRemainingDailyAds();

      return {
        isInitialized: this.rewardedAd !== null,
        isLoaded: this.isLoaded,
        isLoading: this.isLoading,
        canWatchAd,
        remainingAds
      };
    } catch (error) {
      console.error('Error getting ad status:', error);
      return {
        isInitialized: false,
        isLoaded: false,
        isLoading: false,
        canWatchAd: false,
        remainingAds: 0
      };
    }
  }

  static destroy(): void {
    try {
      if (this.rewardedAd) {
        // Remove all listeners
        this.rewardedAd.removeAllListeners();
        this.rewardedAd = null;
      }
      this.isLoaded = false;
      this.isLoading = false;
    } catch (error) {
      console.error('Error destroying ad service:', error);
    }
  }
}
