import AsyncStorage from '@react-native-async-storage/async-storage';
import { CreditBalance, CreditTransaction, CREDIT_CONSTANTS } from '../types/credits';

const CREDIT_BALANCE_KEY = 'WALINK_CREDIT_BALANCE';
const CREDIT_TRANSACTIONS_KEY = 'WALINK_CREDIT_TRANSACTIONS';

export class CreditService {
  private static generateTransactionId(): string {
    return `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private static getTodayString(): string {
    return new Date().toISOString().split('T')[0];
  }

  static async initializeCredits(): Promise<CreditBalance> {
    try {
      const existingBalance = await this.getCreditBalance();
      if (existingBalance.credits === 0 && existingBalance.totalEarned === 0) {
        // First time user - give initial credits
        await this.addCredits(CREDIT_CONSTANTS.INITIAL_CREDITS, 'bonus', 'initial_bonus');
        return await this.getCreditBalance();
      }
      return existingBalance;
    } catch (error) {
      console.error('Error initializing credits:', error);
      throw new Error('Failed to initialize credits');
    }
  }

  static async getCreditBalance(): Promise<CreditBalance> {
    try {
      const data = await AsyncStorage.getItem(CREDIT_BALANCE_KEY);
      if (!data) {
        const initialBalance: CreditBalance = {
          credits: 0,
          lastUpdated: new Date(),
          totalEarned: 0,
          totalSpent: 0,
          dailyAdViews: 0,
          lastAdViewDate: this.getTodayString()
        };
        await AsyncStorage.setItem(CREDIT_BALANCE_KEY, JSON.stringify(initialBalance));
        return initialBalance;
      }
      
      const balance = JSON.parse(data) as CreditBalance;
      // Convert timestamp back to Date object
      balance.lastUpdated = new Date(balance.lastUpdated);
      
      // Reset daily ad views if it's a new day
      const today = this.getTodayString();
      if (balance.lastAdViewDate !== today) {
        balance.dailyAdViews = 0;
        balance.lastAdViewDate = today;
        await this.updateCreditBalance(balance);
      }
      
      return balance;
    } catch (error) {
      console.error('Error getting credit balance:', error);
      throw new Error('Failed to get credit balance');
    }
  }

  private static async updateCreditBalance(balance: CreditBalance): Promise<void> {
    try {
      balance.lastUpdated = new Date();
      await AsyncStorage.setItem(CREDIT_BALANCE_KEY, JSON.stringify(balance));
    } catch (error) {
      console.error('Error updating credit balance:', error);
      throw new Error('Failed to update credit balance');
    }
  }

  static async addCredits(
    amount: number, 
    source: 'ad' | 'purchase' | 'bonus',
    sourceId?: string,
    metadata?: any
  ): Promise<CreditBalance> {
    try {
      const balance = await this.getCreditBalance();
      balance.credits += amount;
      balance.totalEarned += amount;
      
      if (source === 'ad') {
        balance.dailyAdViews += 1;
      }
      
      await this.updateCreditBalance(balance);
      
      // Record transaction
      const transaction: CreditTransaction = {
        id: this.generateTransactionId(),
        type: 'earned',
        source,
        amount,
        timestamp: Date.now(),
        createdAt: new Date(),
        metadata: {
          ...metadata,
          ...(sourceId && { productId: sourceId })
        }
      };
      
      await this.recordTransaction(transaction);
      
      return balance;
    } catch (error) {
      console.error('Error adding credits:', error);
      throw new Error('Failed to add credits');
    }
  }

  static async useCredits(amount: number, originalUrl?: string): Promise<CreditBalance> {
    try {
      const balance = await this.getCreditBalance();
      
      if (balance.credits < amount) {
        throw new Error('Insufficient credits');
      }
      
      balance.credits -= amount;
      balance.totalSpent += amount;
      
      await this.updateCreditBalance(balance);
      
      // Record transaction
      const transaction: CreditTransaction = {
        id: this.generateTransactionId(),
        type: 'spent',
        source: 'shortening',
        amount,
        timestamp: Date.now(),
        createdAt: new Date(),
        metadata: {
          ...(originalUrl && { originalUrl })
        }
      };
      
      await this.recordTransaction(transaction);
      
      return balance;
    } catch (error) {
      console.error('Error using credits:', error);
      throw error;
    }
  }

  static async hasCredits(amount: number = 1): Promise<boolean> {
    try {
      const balance = await this.getCreditBalance();
      return balance.credits >= amount;
    } catch (error) {
      console.error('Error checking credits:', error);
      return false;
    }
  }

  static async canWatchAd(): Promise<boolean> {
    try {
      const balance = await this.getCreditBalance();
      return balance.dailyAdViews < CREDIT_CONSTANTS.MAX_DAILY_ADS;
    } catch (error) {
      console.error('Error checking ad availability:', error);
      return false;
    }
  }

  static async getRemainingDailyAds(): Promise<number> {
    try {
      const balance = await this.getCreditBalance();
      return Math.max(0, CREDIT_CONSTANTS.MAX_DAILY_ADS - balance.dailyAdViews);
    } catch (error) {
      console.error('Error getting remaining ads:', error);
      return 0;
    }
  }

  private static async recordTransaction(transaction: CreditTransaction): Promise<void> {
    try {
      const existingTransactions = await this.getTransactionHistory();
      const updatedTransactions = [transaction, ...existingTransactions];
      
      // Keep only last 100 transactions to prevent storage bloat
      const limitedTransactions = updatedTransactions.slice(0, 100);
      
      await AsyncStorage.setItem(CREDIT_TRANSACTIONS_KEY, JSON.stringify(limitedTransactions));
    } catch (error) {
      console.error('Error recording transaction:', error);
      // Don't throw here as this is not critical for the main flow
    }
  }

  static async getTransactionHistory(): Promise<CreditTransaction[]> {
    try {
      const data = await AsyncStorage.getItem(CREDIT_TRANSACTIONS_KEY);
      if (!data) return [];
      
      const transactions = JSON.parse(data) as CreditTransaction[];
      return transactions.map(tx => ({
        ...tx,
        createdAt: new Date(tx.createdAt)
      }));
    } catch (error) {
      console.error('Error getting transaction history:', error);
      return [];
    }
  }

  static async clearAllData(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([CREDIT_BALANCE_KEY, CREDIT_TRANSACTIONS_KEY]);
    } catch (error) {
      console.error('Error clearing credit data:', error);
      throw new Error('Failed to clear credit data');
    }
  }

  static async getUsageStats(): Promise<{
    totalCreditsEarned: number;
    totalCreditsSpent: number;
    currentBalance: number;
    dailyAdViewsRemaining: number;
    transactionCount: number;
  }> {
    try {
      const balance = await this.getCreditBalance();
      const transactions = await this.getTransactionHistory();
      const remainingAds = await this.getRemainingDailyAds();
      
      return {
        totalCreditsEarned: balance.totalEarned,
        totalCreditsSpent: balance.totalSpent,
        currentBalance: balance.credits,
        dailyAdViewsRemaining: remainingAds,
        transactionCount: transactions.length
      };
    } catch (error) {
      console.error('Error getting usage stats:', error);
      throw new Error('Failed to get usage stats');
    }
  }
}
