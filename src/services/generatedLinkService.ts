import AsyncStorage from '@react-native-async-storage/async-storage';
import { GeneratedLink, GeneratedLinkInput, GeneratedLinkUpdate } from '../types/generatedLink';

const STORAGE_KEY = 'WALINK_GENERATED_LINKS';

export class GeneratedLinkService {
  private static generateId(): string {
    return `link_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private static generateAutoLabel(phoneNumber: string, message: string): string {
    const phone = phoneNumber.replace(/^\+/, '');
    const messagePreview = message.length > 20 ? message.substring(0, 20) + '...' : message;
    return `${phone} - ${messagePreview}`;
  }

  static async getAllLinks(): Promise<GeneratedLink[]> {
    try {
      const data = await AsyncStorage.getItem(STORAGE_KEY);
      if (!data) return [];
      
      const links = JSON.parse(data) as GeneratedLink[];
      // Convert timestamp back to Date objects
      return links.map(link => ({
        ...link,
        createdAt: new Date(link.createdAt)
      })).sort((a, b) => b.timestamp - a.timestamp); // Sort by newest first
    } catch (error) {
      console.error('Error loading generated links:', error);
      return [];
    }
  }

  static async saveLink(input: GeneratedLinkInput): Promise<GeneratedLink> {
    try {
      const existingLinks = await this.getAllLinks();
      
      const newLink: GeneratedLink = {
        id: this.generateId(),
        phoneNumber: input.phoneNumber.trim(),
        message: input.message.trim(),
        originalUrl: input.originalUrl.trim(),
        shortenedUrl: input.shortenedUrl?.trim(),
        label: input.label?.trim() || this.generateAutoLabel(input.phoneNumber, input.message),
        timestamp: Date.now(),
        createdAt: new Date()
      };

      const updatedLinks = [newLink, ...existingLinks];
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updatedLinks));
      
      return newLink;
    } catch (error) {
      console.error('Error saving generated link:', error);
      throw new Error('Failed to save generated link');
    }
  }

  static async updateLink(id: string, updates: GeneratedLinkUpdate): Promise<GeneratedLink | null> {
    try {
      const links = await this.getAllLinks();
      const linkIndex = links.findIndex(link => link.id === id);
      
      if (linkIndex === -1) {
        throw new Error('Generated link not found');
      }

      const updatedLink = {
        ...links[linkIndex],
        ...updates,
        // Ensure these fields cannot be updated
        id: links[linkIndex].id,
        timestamp: links[linkIndex].timestamp,
        createdAt: links[linkIndex].createdAt
      };

      links[linkIndex] = updatedLink;
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(links));
      
      return updatedLink;
    } catch (error) {
      console.error('Error updating generated link:', error);
      throw new Error('Failed to update generated link');
    }
  }

  static async deleteLink(id: string): Promise<boolean> {
    try {
      const links = await this.getAllLinks();
      const filteredLinks = links.filter(link => link.id !== id);
      
      if (filteredLinks.length === links.length) {
        return false; // Link not found
      }

      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(filteredLinks));
      return true;
    } catch (error) {
      console.error('Error deleting generated link:', error);
      throw new Error('Failed to delete generated link');
    }
  }

  static async getLinkById(id: string): Promise<GeneratedLink | null> {
    try {
      const links = await this.getAllLinks();
      return links.find(link => link.id === id) || null;
    } catch (error) {
      console.error('Error getting generated link by ID:', error);
      return null;
    }
  }

  static async clearAllLinks(): Promise<void> {
    try {
      await AsyncStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.error('Error clearing all generated links:', error);
      throw new Error('Failed to clear generated links');
    }
  }

  static async searchLinks(query: string): Promise<GeneratedLink[]> {
    try {
      const allLinks = await this.getAllLinks();
      const lowercaseQuery = query.toLowerCase();
      
      return allLinks.filter(link => 
        link.label?.toLowerCase().includes(lowercaseQuery) ||
        link.phoneNumber.includes(query) ||
        link.message.toLowerCase().includes(lowercaseQuery)
      );
    } catch (error) {
      console.error('Error searching generated links:', error);
      return [];
    }
  }

  static async getRecentLinks(limit: number = 3): Promise<GeneratedLink[]> {
    try {
      const allLinks = await this.getAllLinks();
      return allLinks.slice(0, limit);
    } catch (error) {
      console.error('Error getting recent generated links:', error);
      return [];
    }
  }
}
