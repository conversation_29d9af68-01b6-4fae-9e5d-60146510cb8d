import {
  initConnection,
  purchaseUpdatedListener,
  purchaseErrorListener,
  getProducts,
  requestPurchase,
  finishTransaction,
  Product,
  Purchase,
  PurchaseError,
  SubscriptionPurchase,
} from 'react-native-iap';
import { CreditService } from './creditService';
import { CREDIT_PRODUCTS, PurchaseProduct } from '../types/credits';
import { Alert, Platform } from 'react-native';

export class PurchaseService {
  private static isInitialized = false;
  private static products: Product[] = [];
  private static purchaseUpdateSubscription: any = null;
  private static purchaseErrorSubscription: any = null;

  static async initialize(): Promise<void> {
    try {
      if (this.isInitialized) return;

      // Initialize IAP connection
      await initConnection();
      console.log('IAP connection initialized');

      // Set up purchase listeners
      this.setupPurchaseListeners();

      // Load products
      await this.loadProducts();

      this.isInitialized = true;
    } catch (error) {
      console.error('Error initializing purchase service:', error);
      throw new Error('Failed to initialize purchase service');
    }
  }

  private static setupPurchaseListeners(): void {
    // Listen for purchase updates
    this.purchaseUpdateSubscription = purchaseUpdatedListener(
      async (purchase: Purchase | SubscriptionPurchase) => {
        console.log('Purchase updated:', purchase);
        await this.handlePurchaseUpdate(purchase);
      }
    );

    // Listen for purchase errors
    this.purchaseErrorSubscription = purchaseErrorListener(
      (error: PurchaseError) => {
        console.error('Purchase error:', error);
        this.handlePurchaseError(error);
      }
    );
  }

  private static async handlePurchaseUpdate(purchase: Purchase | SubscriptionPurchase): Promise<void> {
    try {
      const { productId, transactionReceipt } = purchase;
      
      if (transactionReceipt) {
        // Find the product to get credit amount
        const product = CREDIT_PRODUCTS.find(p => p.id === productId);
        if (product) {
          // Award credits to user
          await CreditService.addCredits(
            product.credits,
            'purchase',
            productId,
            { transactionId: purchase.transactionId }
          );

          Alert.alert(
            'Purchase Successful!',
            `You received ${product.credits} credits. Thank you for your purchase!`
          );
        }

        // Finish the transaction
        await finishTransaction({ purchase, isConsumable: true });
      }
    } catch (error) {
      console.error('Error handling purchase update:', error);
      Alert.alert('Error', 'There was an issue processing your purchase. Please contact support.');
    }
  }

  private static handlePurchaseError(error: PurchaseError): void {
    console.error('Purchase error details:', error);
    
    if (error.code === 'E_USER_CANCELLED') {
      // User cancelled, no need to show error
      return;
    }

    Alert.alert(
      'Purchase Failed',
      'There was an issue with your purchase. Please try again or contact support if the problem persists.'
    );
  }

  private static async loadProducts(): Promise<void> {
    try {
      const productIds = CREDIT_PRODUCTS.map(p => p.id);
      const products = await getProducts({ skus: productIds });
      this.products = products;
      console.log('Loaded products:', products);
    } catch (error) {
      console.error('Error loading products:', error);
      // Don't throw here as the app can still function without IAP
    }
  }

  static async getAvailableProducts(): Promise<PurchaseProduct[]> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      // Merge our product definitions with store data
      return CREDIT_PRODUCTS.map(creditProduct => {
        const storeProduct = this.products.find(p => p.productId === creditProduct.id);
        return {
          ...creditProduct,
          price: storeProduct?.localizedPrice || creditProduct.price,
          title: storeProduct?.title || creditProduct.title,
          description: storeProduct?.description || creditProduct.description,
        };
      });
    } catch (error) {
      console.error('Error getting available products:', error);
      return CREDIT_PRODUCTS; // Return default products as fallback
    }
  }

  static async purchaseProduct(productId: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      // Check if product exists
      const product = CREDIT_PRODUCTS.find(p => p.id === productId);
      if (!product) {
        return {
          success: false,
          error: 'Product not found'
        };
      }

      // Request purchase
      await requestPurchase({ sku: productId });
      
      // The actual purchase handling is done in the listener
      return { success: true };

    } catch (error) {
      console.error('Error purchasing product:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('E_USER_CANCELLED')) {
          return {
            success: false,
            error: 'Purchase cancelled'
          };
        }
        
        if (error.message.includes('E_NETWORK_ERROR')) {
          return {
            success: false,
            error: 'Network error. Please check your connection.'
          };
        }
      }

      return {
        success: false,
        error: 'Purchase failed. Please try again.'
      };
    }
  }

  static async showPurchaseOptions(): Promise<boolean> {
    try {
      const products = await this.getAvailableProducts();
      
      return new Promise((resolve) => {
        const options = products.map(product => ({
          text: `${product.title} - ${product.price}`,
          onPress: async () => {
            const result = await this.purchaseProduct(product.id);
            resolve(result.success);
          }
        }));

        options.push({
          text: 'Cancel',
          onPress: () => resolve(false),
          style: 'cancel' as any
        });

        Alert.alert(
          'Purchase Credits',
          'Choose a credit package:',
          options
        );
      });
    } catch (error) {
      console.error('Error showing purchase options:', error);
      Alert.alert('Error', 'Unable to load purchase options. Please try again.');
      return false;
    }
  }

  static async isAvailable(): Promise<boolean> {
    try {
      return this.isInitialized && this.products.length > 0;
    } catch (error) {
      console.error('Error checking purchase availability:', error);
      return false;
    }
  }

  static async restorePurchases(): Promise<{
    success: boolean;
    restoredCount: number;
    error?: string;
  }> {
    try {
      // Note: This is a simplified implementation
      // In a real app, you'd want to restore purchases from the store
      // and validate them with your backend
      
      return {
        success: true,
        restoredCount: 0,
        error: 'No purchases to restore'
      };
    } catch (error) {
      console.error('Error restoring purchases:', error);
      return {
        success: false,
        restoredCount: 0,
        error: 'Failed to restore purchases'
      };
    }
  }

  static destroy(): void {
    try {
      if (this.purchaseUpdateSubscription) {
        this.purchaseUpdateSubscription.remove();
        this.purchaseUpdateSubscription = null;
      }

      if (this.purchaseErrorSubscription) {
        this.purchaseErrorSubscription.remove();
        this.purchaseErrorSubscription = null;
      }

      this.isInitialized = false;
      this.products = [];
    } catch (error) {
      console.error('Error destroying purchase service:', error);
    }
  }
}
