import AsyncStorage from '@react-native-async-storage/async-storage';
import { SavedMessage, SavedMessageInput, SavedMessageUpdate } from '../types/savedMessage';

const STORAGE_KEY = 'QUICKCHAT_SAVED_MESSAGES';

export class SavedMessageService {
  private static generateId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  static async initializeDefaultMessages(): Promise<void> {
    try {
      const existingMessages = await this.getAllMessages();

      // Only add default message if no messages exist
      if (existingMessages.length === 0) {
        const defaultMessage: SavedMessageInput = {
          title: 'hi',
          content: 'hi'
        };

        await this.saveMessage(defaultMessage);
      }
    } catch (error) {
      console.error('Error initializing default messages:', error);
    }
  }

  static async getAllMessages(): Promise<SavedMessage[]> {
    try {
      const data = await AsyncStorage.getItem(STORAGE_KEY);
      if (!data) return [];
      
      const messages = JSON.parse(data) as SavedMessage[];
      // Convert timestamp back to Date objects
      return messages.map(msg => ({
        ...msg,
        createdAt: new Date(msg.createdAt)
      })).sort((a, b) => b.timestamp - a.timestamp); // Sort by newest first
    } catch (error) {
      console.error('Error loading saved messages:', error);
      return [];
    }
  }

  static async saveMessage(input: SavedMessageInput): Promise<SavedMessage> {
    try {
      const existingMessages = await this.getAllMessages();
      
      const newMessage: SavedMessage = {
        id: this.generateId(),
        title: input.title.trim(),
        content: input.content.trim(),
        timestamp: Date.now(),
        createdAt: new Date()
      };

      const updatedMessages = [newMessage, ...existingMessages];
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updatedMessages));
      
      return newMessage;
    } catch (error) {
      console.error('Error saving message:', error);
      throw new Error('Failed to save message');
    }
  }

  static async updateMessage(id: string, updates: SavedMessageUpdate): Promise<SavedMessage | null> {
    try {
      const messages = await this.getAllMessages();
      const messageIndex = messages.findIndex(msg => msg.id === id);
      
      if (messageIndex === -1) {
        throw new Error('Message not found');
      }

      const updatedMessage = {
        ...messages[messageIndex],
        ...updates,
        // Ensure these fields cannot be updated
        id: messages[messageIndex].id,
        timestamp: messages[messageIndex].timestamp,
        createdAt: messages[messageIndex].createdAt
      };

      messages[messageIndex] = updatedMessage;
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(messages));
      
      return updatedMessage;
    } catch (error) {
      console.error('Error updating message:', error);
      throw new Error('Failed to update message');
    }
  }

  static async deleteMessage(id: string): Promise<boolean> {
    try {
      const messages = await this.getAllMessages();
      const filteredMessages = messages.filter(msg => msg.id !== id);
      
      if (filteredMessages.length === messages.length) {
        return false; // Message not found
      }

      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(filteredMessages));
      return true;
    } catch (error) {
      console.error('Error deleting message:', error);
      throw new Error('Failed to delete message');
    }
  }

  static async getMessageById(id: string): Promise<SavedMessage | null> {
    try {
      const messages = await this.getAllMessages();
      return messages.find(msg => msg.id === id) || null;
    } catch (error) {
      console.error('Error getting message by ID:', error);
      return null;
    }
  }

  static async clearAllMessages(): Promise<void> {
    try {
      await AsyncStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.error('Error clearing all messages:', error);
      throw new Error('Failed to clear messages');
    }
  }
}
