import AsyncStorage from '@react-native-async-storage/async-storage';
import { ShortenedUrl, ShorteningRequest, ShorteningResult, SHORTENING_CONSTANTS } from '../types/shortening';
import { CreditService } from './creditService';

const SHORTENED_URLS_KEY = 'WALINK_SHORTENED_URLS';
const URL_MAPPING_KEY = 'WALINK_URL_MAPPING';

export class ShorteningService {
  private static generateId(): string {
    return `short_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private static generateShortCode(length: number = SHORTENING_CONSTANTS.CODE_LENGTH): string {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  private static async isCodeUnique(code: string): Promise<boolean> {
    try {
      const mapping = await this.getUrlMapping();
      return !mapping.hasOwnProperty(code);
    } catch (error) {
      console.error('Error checking code uniqueness:', error);
      return false;
    }
  }

  private static async getUniqueShortCode(): Promise<string> {
    let attempts = 0;
    const maxAttempts = 10;
    
    while (attempts < maxAttempts) {
      const code = this.generateShortCode();
      const isUnique = await this.isCodeUnique(code);
      if (isUnique) {
        return code;
      }
      attempts++;
    }
    
    // If we can't find a unique code, increase length
    return this.generateShortCode(SHORTENING_CONSTANTS.CODE_LENGTH + 1);
  }

  private static async getUrlMapping(): Promise<{ [shortCode: string]: string }> {
    try {
      const data = await AsyncStorage.getItem(URL_MAPPING_KEY);
      return data ? JSON.parse(data) : {};
    } catch (error) {
      console.error('Error getting URL mapping:', error);
      return {};
    }
  }

  private static async updateUrlMapping(shortCode: string, urlId: string): Promise<void> {
    try {
      const mapping = await this.getUrlMapping();
      mapping[shortCode] = urlId;
      await AsyncStorage.setItem(URL_MAPPING_KEY, JSON.stringify(mapping));
    } catch (error) {
      console.error('Error updating URL mapping:', error);
      throw new Error('Failed to update URL mapping');
    }
  }

  static async shortenUrl(request: ShorteningRequest): Promise<ShorteningResult> {
    try {
      // Check if user has credits
      const hasCredits = await CreditService.hasCredits(1);
      if (!hasCredits) {
        return {
          success: false,
          error: 'Insufficient credits',
          creditsUsed: 0,
          remainingCredits: (await CreditService.getCreditBalance()).credits
        };
      }

      // Generate unique short code
      const shortCode = request.customCode && await this.isCodeUnique(request.customCode) 
        ? request.customCode 
        : await this.getUniqueShortCode();

      // Create shortened URL object
      const shortenedUrl: ShortenedUrl = {
        id: this.generateId(),
        shortCode,
        originalUrl: request.originalUrl,
        customDomain: SHORTENING_CONSTANTS.BASE_DOMAIN,
        fullShortUrl: `https://${SHORTENING_CONSTANTS.BASE_DOMAIN}${SHORTENING_CONSTANTS.SHORT_PATH}${shortCode}`,
        createdAt: new Date(),
        timestamp: Date.now(),
        clickCount: 0,
        isActive: true,
        metadata: {
          phoneNumber: request.phoneNumber,
          message: request.message
        }
      };

      // Save to storage
      await this.saveShortenedUrl(shortenedUrl);
      await this.updateUrlMapping(shortCode, shortenedUrl.id);

      // Use credit
      const updatedBalance = await CreditService.useCredits(1, request.originalUrl);

      return {
        success: true,
        shortUrl: shortenedUrl.fullShortUrl,
        shortCode: shortenedUrl.shortCode,
        creditsUsed: 1,
        remainingCredits: updatedBalance.credits
      };

    } catch (error) {
      console.error('Error shortening URL:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to shorten URL',
        creditsUsed: 0,
        remainingCredits: (await CreditService.getCreditBalance()).credits
      };
    }
  }

  private static async saveShortenedUrl(shortenedUrl: ShortenedUrl): Promise<void> {
    try {
      const existingUrls = await this.getAllShortenedUrls();
      const updatedUrls = [shortenedUrl, ...existingUrls];
      
      // Keep only the most recent URLs to prevent storage bloat
      const limitedUrls = updatedUrls.slice(0, SHORTENING_CONSTANTS.MAX_URLS_PER_USER);
      
      await AsyncStorage.setItem(SHORTENED_URLS_KEY, JSON.stringify(limitedUrls));
    } catch (error) {
      console.error('Error saving shortened URL:', error);
      throw new Error('Failed to save shortened URL');
    }
  }

  static async getAllShortenedUrls(): Promise<ShortenedUrl[]> {
    try {
      const data = await AsyncStorage.getItem(SHORTENED_URLS_KEY);
      if (!data) return [];
      
      const urls = JSON.parse(data) as ShortenedUrl[];
      return urls.map(url => ({
        ...url,
        createdAt: new Date(url.createdAt),
        lastClickedAt: url.lastClickedAt ? new Date(url.lastClickedAt) : undefined
      })).sort((a, b) => b.timestamp - a.timestamp);
    } catch (error) {
      console.error('Error getting shortened URLs:', error);
      return [];
    }
  }

  static async getShortenedUrlByCode(shortCode: string): Promise<ShortenedUrl | null> {
    try {
      const mapping = await this.getUrlMapping();
      const urlId = mapping[shortCode];
      
      if (!urlId) return null;
      
      const urls = await this.getAllShortenedUrls();
      return urls.find(url => url.id === urlId) || null;
    } catch (error) {
      console.error('Error getting shortened URL by code:', error);
      return null;
    }
  }

  static async recordClick(shortCode: string): Promise<string | null> {
    try {
      const shortenedUrl = await this.getShortenedUrlByCode(shortCode);
      if (!shortenedUrl || !shortenedUrl.isActive) {
        return null;
      }

      // Update click count
      shortenedUrl.clickCount += 1;
      shortenedUrl.lastClickedAt = new Date();

      // Save updated URL
      const allUrls = await this.getAllShortenedUrls();
      const urlIndex = allUrls.findIndex(url => url.id === shortenedUrl.id);
      if (urlIndex !== -1) {
        allUrls[urlIndex] = shortenedUrl;
        await AsyncStorage.setItem(SHORTENED_URLS_KEY, JSON.stringify(allUrls));
      }

      return shortenedUrl.originalUrl;
    } catch (error) {
      console.error('Error recording click:', error);
      return null;
    }
  }

  static async deleteShortenedUrl(shortCode: string): Promise<boolean> {
    try {
      const allUrls = await this.getAllShortenedUrls();
      const filteredUrls = allUrls.filter(url => url.shortCode !== shortCode);
      
      if (filteredUrls.length === allUrls.length) {
        return false; // URL not found
      }

      await AsyncStorage.setItem(SHORTENED_URLS_KEY, JSON.stringify(filteredUrls));
      
      // Remove from mapping
      const mapping = await this.getUrlMapping();
      delete mapping[shortCode];
      await AsyncStorage.setItem(URL_MAPPING_KEY, JSON.stringify(mapping));
      
      return true;
    } catch (error) {
      console.error('Error deleting shortened URL:', error);
      throw new Error('Failed to delete shortened URL');
    }
  }

  static async getUrlStats(): Promise<{
    totalUrls: number;
    totalClicks: number;
    activeUrls: number;
    topUrls: Array<{ shortCode: string; originalUrl: string; clicks: number; }>;
  }> {
    try {
      const urls = await this.getAllShortenedUrls();
      const activeUrls = urls.filter(url => url.isActive);
      const totalClicks = urls.reduce((sum, url) => sum + url.clickCount, 0);
      
      const topUrls = urls
        .sort((a, b) => b.clickCount - a.clickCount)
        .slice(0, 5)
        .map(url => ({
          shortCode: url.shortCode,
          originalUrl: url.originalUrl,
          clicks: url.clickCount
        }));

      return {
        totalUrls: urls.length,
        totalClicks,
        activeUrls: activeUrls.length,
        topUrls
      };
    } catch (error) {
      console.error('Error getting URL stats:', error);
      throw new Error('Failed to get URL stats');
    }
  }

  static async clearAllData(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([SHORTENED_URLS_KEY, URL_MAPPING_KEY]);
    } catch (error) {
      console.error('Error clearing shortening data:', error);
      throw new Error('Failed to clear shortening data');
    }
  }
}
