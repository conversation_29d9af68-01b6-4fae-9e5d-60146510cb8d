export interface CreditBalance {
  credits: number;
  lastUpdated: Date;
  totalEarned: number;
  totalSpent: number;
  dailyAdViews: number;
  lastAdViewDate: string; // YYYY-MM-DD format
}

export interface CreditTransaction {
  id: string;
  type: 'earned' | 'spent';
  source: 'ad' | 'purchase' | 'bonus' | 'shortening';
  amount: number;
  timestamp: number;
  createdAt: Date;
  metadata?: {
    productId?: string;
    adUnitId?: string;
    originalUrl?: string;
  };
}

export interface PurchaseProduct {
  id: string;
  credits: number;
  price: string;
  title: string;
  description: string;
}

export const CREDIT_PRODUCTS: PurchaseProduct[] = [
  {
    id: 'walink_credits_small',
    credits: 3,
    price: '$0.99',
    title: 'Small Pack',
    description: '3 Link Credits'
  },
  {
    id: 'walink_credits_medium',
    credits: 10,
    price: '$2.99',
    title: 'Medium Pack',
    description: '10 Link Credits'
  },
  {
    id: 'walink_credits_large',
    credits: 25,
    price: '$5.99',
    title: 'Large Pack',
    description: '25 Link Credits'
  }
];

export const CREDIT_CONSTANTS = {
  INITIAL_CREDITS: 1,
  AD_REWARD_CREDITS: 1,
  SHORTENING_COST: 1,
  MAX_DAILY_ADS: 2,
  AD_COOLDOWN_HOURS: 1
};
