export interface GeneratedLink {
  id: string;
  phoneNumber: string;
  message: string;
  originalUrl: string;
  shortenedUrl?: string;
  label?: string; // Custom label for the link
  timestamp: number;
  createdAt: Date;
}

export interface GeneratedLinkInput {
  phoneNumber: string;
  message: string;
  originalUrl: string;
  shortenedUrl?: string;
  label?: string;
}

export type GeneratedLinkUpdate = Partial<Omit<GeneratedLink, 'id' | 'timestamp' | 'createdAt'>>;
