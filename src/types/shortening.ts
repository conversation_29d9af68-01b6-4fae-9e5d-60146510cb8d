export interface ShortenedUrl {
  id: string;
  shortCode: string;
  originalUrl: string;
  customDomain: string;
  fullShortUrl: string;
  createdAt: Date;
  timestamp: number;
  clickCount: number;
  lastClickedAt?: Date;
  isActive: boolean;
  metadata?: {
    phoneNumber?: string;
    message?: string;
    userAgent?: string;
    ipAddress?: string;
  };
}

export interface ShorteningRequest {
  originalUrl: string;
  phoneNumber?: string;
  message?: string;
  customCode?: string;
}

export interface ShorteningResult {
  success: boolean;
  shortUrl?: string;
  shortCode?: string;
  error?: string;
  creditsUsed: number;
  remainingCredits: number;
}

export interface ClickAnalytics {
  totalClicks: number;
  uniqueClicks: number;
  clicksByDate: { [date: string]: number };
  clicksByHour: { [hour: string]: number };
  topUrls: Array<{
    shortCode: string;
    originalUrl: string;
    clicks: number;
  }>;
}

export const SHORTENING_CONSTANTS = {
  BASE_DOMAIN: 'walink.app',
  SHORT_PATH: '/s/',
  CODE_LENGTH: 6,
  MAX_URLS_PER_USER: 1000,
  ANALYTICS_RETENTION_DAYS: 30
};
