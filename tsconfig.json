{"compilerOptions": {"target": "esnext", "module": "esnext", "lib": ["esnext"], "jsx": "react-native", "strict": true, "moduleResolution": "node", "baseUrl": ".", "paths": {"*": ["src/*"]}, "allowJs": true, "esModuleInterop": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "noEmit": true}, "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}